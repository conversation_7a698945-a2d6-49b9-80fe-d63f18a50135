#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Excel导出模块
用于将计算数据导出为Excel格式
"""

import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
from typing import Dict, List
import os

class ExcelExporter:
    """Excel导出器"""
    
    def __init__(self):
        """初始化导出器"""
        self.setup_styles()
    
    def setup_styles(self):
        """设置样式"""
        # 标题样式
        self.title_font = Font(name='微软雅黑', size=14, bold=True)
        self.header_font = Font(name='微软雅黑', size=11, bold=True)
        self.normal_font = Font(name='微软雅黑', size=10)
        
        # 对齐样式
        self.center_alignment = Alignment(horizontal='center', vertical='center')
        self.left_alignment = Alignment(horizontal='left', vertical='center')
        
        # 边框样式
        thin_border = Side(border_style="thin", color="000000")
        self.border = Border(top=thin_border, bottom=thin_border, left=thin_border, right=thin_border)
        
        # 填充样式
        self.header_fill = PatternFill(start_color="E6E6FA", end_color="E6E6FA", fill_type="solid")
        self.data_fill = PatternFill(start_color="F0F8FF", end_color="F0F8FF", fill_type="solid")
    
    def export_single_project(self, project_data: Dict, file_path: str) -> bool:
        """导出单个项目数据"""
        try:
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = project_data.get('station_name', '未命名站点')
            
            self._write_project_data(ws, project_data)
            
            wb.save(file_path)
            return True
            
        except Exception as e:
            print(f"导出单个项目时出错: {e}")
            return False
    
    def export_multiple_projects(self, projects_data: List[Dict], file_path: str) -> bool:
        """导出多个项目数据"""
        try:
            wb = openpyxl.Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            for project_data in projects_data:
                station_name = project_data.get('station_name', '未命名站点')
                ws = wb.create_sheet(title=station_name)
                self._write_project_data(ws, project_data)
            
            wb.save(file_path)
            return True
            
        except Exception as e:
            print(f"导出多个项目时出错: {e}")
            return False
    
    def _write_project_data(self, ws, project_data: Dict):
        """写入项目数据到工作表"""
        # 设置列宽
        ws.column_dimensions['A'].width = 25
        for col in ['B', 'C', 'D', 'E', 'F', 'G']:
            ws.column_dimensions[col].width = 12
        
        row = 1
        
        # 标题
        ws.cell(row=row, column=1, value='站台宽度、疏散时间计算').font = self.title_font
        ws.cell(row=row, column=1).alignment = self.center_alignment
        row += 1
        
        # 客流数据表头
        ws.cell(row=row, column=1, value='客流数据（人）').font = self.header_font
        ws.cell(row=row, column=1).alignment = self.center_alignment

        ws.cell(row=row, column=2, value='上行').font = self.header_font
        ws.cell(row=row, column=2).alignment = self.center_alignment

        ws.cell(row=row, column=5, value='下行').font = self.header_font
        ws.cell(row=row, column=5).alignment = self.center_alignment
        
        row += 1
        
        # 客流数据子表头
        headers = ['', '上客', '下客', '断面', '上客', '下客', '断面']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = self.header_font
            cell.alignment = self.center_alignment
        
        row += 1
        
        # 客流数据内容
        periods = ['初期', '近期', '远期']
        for period in periods:
            period_key = {'初期': 'initial', '近期': 'near', '远期': 'far'}[period]
            
            ws.cell(row=row, column=1, value=period).font = self.normal_font
            ws.cell(row=row, column=2, value=project_data.get(f'{period_key}_up_in', 0))
            ws.cell(row=row, column=3, value=project_data.get(f'{period_key}_up_out', 0))
            ws.cell(row=row, column=4, value=project_data.get(f'{period_key}_up_area', 0))
            ws.cell(row=row, column=5, value=project_data.get(f'{period_key}_down_in', 0))
            ws.cell(row=row, column=6, value=project_data.get(f'{period_key}_down_out', 0))
            ws.cell(row=row, column=7, value=project_data.get(f'{period_key}_down_area', 0))
            row += 1
        
        row += 1
        
        # 计算参数表头
        ws.cell(row=row, column=1, value='计算参数').font = self.header_font
        ws.cell(row=row, column=1).alignment = self.center_alignment
        
        ws.cell(row=row, column=2, value='初期').font = self.header_font
        ws.cell(row=row, column=3, value='近期').font = self.header_font
        ws.cell(row=row, column=4, value='远期').font = self.header_font
        ws.cell(row=row, column=5, value='单位').font = self.header_font
        
        row += 1
        row += 1
        
        # 计算参数内容
        params = [
            ('高峰系数', 'k', '/'),
            ('列车对数', 'n_train', '/'),
            ('站台人流密度ρ', 'p', '人/m²'),
            ('站台长度L', 'l', 'm'),
            ('站台边缘到列车全部停稳内侧距离M', 'm', 'm'),
            ('一台自动扶梯的通过能力A1', 'a_1', '人/h·m'),
            ('疏散楼梯的通过能力A2', 'a_2', '人/h·m'),
            ('自动扶梯台数N', 'n_auto', '/'),
            ('疏散楼梯宽度B', 'b', 'm'),
            ('疏散楼梯数量', 'n_stair', '/')
        ]
        
        for param_name, param_key, unit in params:
            ws.cell(row=row, column=1, value=param_name).font = self.normal_font
            
            if param_key == 'k':
                ws.cell(row=row, column=2, value=project_data.get('k_initial', 1.13))
                ws.cell(row=row, column=3, value=project_data.get('k_near', 1.13))
                ws.cell(row=row, column=4, value=project_data.get('k_far', 1.13))
            else:
                value = project_data.get(param_key, 0)
                ws.cell(row=row, column=2, value=value)
                ws.cell(row=row, column=3, value=value)
                ws.cell(row=row, column=4, value=value)
            
            ws.cell(row=row, column=5, value=unit).font = self.normal_font
            row += 1
        
        row += 1
        
        # 中间计算值表头
        ws.cell(row=row, column=1, value='中间计算值').font = self.header_font
        ws.cell(row=row, column=1).alignment = self.center_alignment
        
        ws.cell(row=row, column=2, value='初期').font = self.header_font
        ws.cell(row=row, column=3, value='近期').font = self.header_font
        ws.cell(row=row, column=4, value='远期').font = self.header_font
        ws.cell(row=row, column=5, value='单位').font = self.header_font
        
        row += 1
        row += 1
        
        # 中间计算值内容
        intermediate_values = [
            ('单侧上下客合计（上行）', 'up_add', '人'),
            ('单侧上下客合计（下行）', 'down_add', '人'),
            ('上客流量合计', 'in_add', '人')
        ]
        
        for value_name, value_key, unit in intermediate_values:
            ws.cell(row=row, column=1, value=value_name).font = self.normal_font
            ws.cell(row=row, column=2, value=project_data.get(f'initial_{value_key}', 0))
            ws.cell(row=row, column=3, value=project_data.get(f'near_{value_key}', 0))
            ws.cell(row=row, column=4, value=project_data.get(f'far_{value_key}', 0))
            ws.cell(row=row, column=5, value=unit).font = self.normal_font
            row += 1
        
        row += 1
        
        # 最终计算结果表头
        ws.cell(row=row, column=1, value='最终计算结果').font = self.header_font
        ws.cell(row=row, column=1).alignment = self.center_alignment
        
        ws.cell(row=row, column=2, value='初期').font = self.header_font
        ws.cell(row=row, column=3, value='近期').font = self.header_font
        ws.cell(row=row, column=4, value='远期').font = self.header_font
        ws.cell(row=row, column=5, value='单位').font = self.header_font
        
        row += 1
        row += 1
        
        # 最终计算结果内容
        final_results = [
            ('站台宽度b1', 'b_1', 'm'),
            ('站台宽度b2', 'b_2', 'm'),
            ('Q1', 'q_1', '人'),
            ('Q2', 'q_2', '人'),
            ('疏散时间T', 't', 'min')
        ]
        
        for result_name, result_key, unit in final_results:
            ws.cell(row=row, column=1, value=result_name).font = self.normal_font
            ws.cell(row=row, column=2, value=project_data.get(f'initial_{result_key}', 0))
            ws.cell(row=row, column=3, value=project_data.get(f'near_{result_key}', 0))
            ws.cell(row=row, column=4, value=project_data.get(f'far_{result_key}', 0))
            ws.cell(row=row, column=5, value=unit).font = self.normal_font
            row += 1
        
        # 应用边框和样式
        self._apply_styles(ws, row - 1)
    
    def _apply_styles(self, ws, max_row: int):
        """应用样式到工作表"""
        for row in range(1, max_row + 1):
            for col in range(1, 8):
                cell = ws.cell(row=row, column=col)
                cell.border = self.border
                
                # 根据内容设置样式
                if row == 1:  # 标题行
                    cell.fill = self.header_fill
                elif cell.value and isinstance(cell.value, str) and any(x in cell.value for x in ['客流数据', '计算参数', '中间计算值', '最终计算结果']):
                    cell.fill = self.header_fill
                elif isinstance(cell.value, (int, float)):
                    cell.alignment = self.center_alignment
