#!/usr/bin/env python
# -*- coding: utf-8 -*-

class MetroEvacuationCalculator:
    """地铁站台疏散时间计算器"""
    
    def __init__(self):
        """初始化计算器"""
        # 输入参数
        self.up_in = 0          # 上行进客
        self.up_out = 0         # 上行出客
        self.up_area = 0        # 上行断面
        self.down_in = 0        # 下行进客
        self.down_out = 0       # 下行出客
        self.down_area = 0      # 下行断面
        
        # 计算参数
        self.k = 1.13           # 高峰系数
        self.n_train = 20       # 列车对数
        self.p = 0.5            # 站台人流密度(人/m²)
        self.l = 135.6          # 站台长度(m)
        self.m = 0.3            # 站台边缘安全门内侧距离(m)
        self.a_1 = 7300         # 自动扶梯通过能力(人/h·m)
        self.a_2 = 3700         # 疏散楼梯通过能力(人/h·m)
        self.n_auto = 2         # 自动扶梯台数
        self.b = 3.3            # 疏散楼梯宽度(m)
        self.n_stair = 1        # 疏散楼梯数
        
        # 计算结果
        self.up_add = 0         # 上行单侧上下客合计
        self.down_add = 0       # 下行单侧上下客合计
        self.in_add = 0         # 上客流量合计
        self.b_1 = 0            # 站台宽度b1(m)
        self.b_2 = 0            # 站台宽度b2(m)
        self.q_1 = 0            # Q1(人)
        self.q_2 = 0            # Q2(人)
        self.t = 0              # 疏散时间T(min)
    
    def set_passenger_flow(self, up_in, up_out, up_area, down_in, down_out, down_area):
        """设置客流数据"""
        self.up_in = up_in
        self.up_out = up_out
        self.up_area = up_area
        self.down_in = down_in
        self.down_out = down_out
        self.down_area = down_area
    
    def set_parameters(self, k=None, n_train=None, p=None, l=None, m=None, 
                      a_1=None, a_2=None, n_auto=None, b=None, n_stair=None):
        """设置计算参数"""
        if k is not None: self.k = k
        if n_train is not None: self.n_train = n_train
        if p is not None: self.p = p
        if l is not None: self.l = l
        if m is not None: self.m = m
        if a_1 is not None: self.a_1 = a_1
        if a_2 is not None: self.a_2 = a_2
        if n_auto is not None: self.n_auto = n_auto
        if b is not None: self.b = b
        if n_stair is not None: self.n_stair = n_stair
    
    def calculate_intermediate_values(self):
        """计算中间值"""
        # 计算单侧上下客合计
        self.up_add = self.up_in + self.up_out
        self.down_add = self.down_in + self.down_out
        self.in_add = self.up_in + self.down_in
    
    def calculate_platform_width(self):
        """计算站台宽度"""
        # 基于Excel表格中的精确公式
        # b1 = C8*C13/C14*C15/C16+C17 = up_add*k/n_train*p/l+m
        # b2 = C9*C13/C14*C15/C16+C17 = down_add*k/n_train*p/l+m

        if self.l > 0 and self.n_train > 0:
            # b1计算：上行进出站客流合计相关
            # 公式：=C8*C13/C14*C15/C16+C17
            self.b_1 = (self.up_add * self.k / self.n_train * self.p / self.l) + self.m

            # b2计算：下行进出站客流合计相关
            # 公式：=C9*C13/C14*C15/C16+C17
            self.b_2 = (self.down_add * self.k / self.n_train * self.p / self.l) + self.m
        else:
            self.b_1 = 0
            self.b_2 = 0
    
    def calculate_evacuation_flow(self):
        """计算疏散人流"""
        # 基于Excel表格中的精确公式
        # Q1 = IF(C4<C7,C7/C14*C13,C4/C14*C13) = IF(up_area<down_area, down_area/n_train*k, up_area/n_train*k)
        # Q2 = C10/C14*C13 = in_add/n_train*k

        if self.n_train > 0:
            # Q1：根据上行和下行断面客流的大小关系计算
            # 公式：=IF(C4<C7,C7/C14*C13,C4/C14*C13)
            if self.up_area < self.down_area:
                self.q_1 = (self.down_area / self.n_train) * self.k
            else:
                self.q_1 = (self.up_area / self.n_train) * self.k

            # Q2：进站客流合计相关
            # 公式：=C10/C14*C13
            self.q_2 = (self.in_add / self.n_train) * self.k
        else:
            self.q_1 = 0
            self.q_2 = 0
    
    def calculate_evacuation_time(self):
        """计算疏散时间"""
        # 基于Excel表格中的精确公式
        # T = (C27+C28)/0.9/(C18/60*(C20-1)+C19/60*C22*C21)
        # T = (q_1+q_2)/0.9/(a_1/60*(n_auto-1)+a_2/60*n_stair*b)

        # 计算分母：疏散能力
        # 自动扶梯能力：a_1/60*(n_auto-1)
        escalator_capacity_per_min = (self.a_1 / 60) * (self.n_auto - 1)

        # 疏散楼梯能力：a_2/60*n_stair*b
        stair_capacity_per_min = (self.a_2 / 60) * self.n_stair * self.b

        # 总疏散能力（人/分钟）
        total_capacity_per_min = escalator_capacity_per_min + stair_capacity_per_min

        if total_capacity_per_min > 0:
            # 疏散时间计算
            # 公式：=(C27+C28)/0.9/(C18/60*(C20-1)+C19/60*C22*C21)
            self.t = (self.q_1 + self.q_2) / 0.9 / total_capacity_per_min
        else:
            self.t = 0
    
    def calculate_all(self):
        """执行所有计算"""
        self.calculate_intermediate_values()
        self.calculate_platform_width()
        self.calculate_evacuation_flow()
        self.calculate_evacuation_time()
    
    def get_results(self):
        """获取计算结果"""
        return {
            'up_add': self.up_add,
            'down_add': self.down_add,
            'in_add': self.in_add,
            'b_1': self.b_1,
            'b_2': self.b_2,
            'q_1': self.q_1,
            'q_2': self.q_2,
            't': self.t
        }
    
    def get_input_parameters(self):
        """获取输入参数"""
        return {
            'up_in': self.up_in,
            'up_out': self.up_out,
            'up_area': self.up_area,
            'down_in': self.down_in,
            'down_out': self.down_out,
            'down_area': self.down_area,
            'k': self.k,
            'n_train': self.n_train,
            'p': self.p,
            'l': self.l,
            'm': self.m,
            'a_1': self.a_1,
            'a_2': self.a_2,
            'n_auto': self.n_auto,
            'b': self.b,
            'n_stair': self.n_stair
        }
    
    def print_results(self):
        """打印计算结果"""
        print("=== 地铁站台疏散时间计算结果 ===")
        print(f"上行单侧上下客合计: {self.up_add:.2f} 人")
        print(f"下行单侧上下客合计: {self.down_add:.2f} 人")
        print(f"上客流量合计: {self.in_add:.2f} 人")
        print(f"站台宽度b1: {self.b_1:.6f} m")
        print(f"站台宽度b2: {self.b_2:.6f} m")
        print(f"Q1: {self.q_1:.4f} 人")
        print(f"Q2: {self.q_2:.4f} 人")
        print(f"疏散时间T: {self.t:.6f} min")


def test_calculator():
    """测试计算器"""
    print("=== 测试地铁疏散计算器 ===")
    
    # 创建计算器实例
    calc = MetroEvacuationCalculator()
    
    # 设置示例数据（来自Excel表格）
    calc.set_passenger_flow(
        up_in=2767, up_out=331, up_area=7785,
        down_in=654, down_out=739, down_area=6393
    )
    
    # 设置计算参数
    calc.set_parameters(
        k=1.13, n_train=20, p=0.5, l=135.6, m=0.3,
        a_1=7300, a_2=3700, n_auto=2, b=3.3, n_stair=1
    )
    
    # 执行计算
    calc.calculate_all()
    
    # 打印结果
    calc.print_results()
    
    # 与Excel中的结果对比
    print("\n=== 与Excel结果对比 ===")
    print("使用精确的Excel公式计算:")
    print(f"b1 - 计算值: {calc.b_1:.6f}, Excel值: 0.945417, 误差: {abs(calc.b_1-0.945417)/0.945417*100:.4f}%")
    print(f"b2 - 计算值: {calc.b_2:.6f}, Excel值: 0.590208, 误差: {abs(calc.b_2-0.590208)/0.590208*100:.4f}%")
    print(f"Q1 - 计算值: {calc.q_1:.4f}, Excel值: 439.8525, 误差: {abs(calc.q_1-439.8525)/439.8525*100:.4f}%")
    print(f"Q2 - 计算值: {calc.q_2:.4f}, Excel值: 193.2865, 误差: {abs(calc.q_2-193.2865)/193.2865*100:.4f}%")
    print(f"T - 计算值: {calc.t:.6f}, Excel值: 2.163468, 误差: {abs(calc.t-2.163468)/2.163468*100:.4f}%")


if __name__ == "__main__":
    test_calculator()
