# 地铁站台疏散时间计算器 V2.0

## 概述

地铁站台疏散时间计算器 V2.0 是一个专业的地铁站台疏散分析工具，支持三个阶段（初期、近期、远期）的计算，具备数据库存储、Excel导出等完整功能。

## 新功能特点

### 🚀 V2.0 重大升级

1. **三阶段计算支持**
   - 初期、近期、远期三个发展阶段
   - 每个阶段独立的客流数据和计算参数
   - 支持不同阶段的高峰系数设置

2. **数据库存储系统**
   - SQLite数据库存储项目数据
   - 支持项目保存、读取、删除
   - 数据覆盖保护和另存为功能
   - 项目搜索功能

3. **Excel导出功能**
   - 单项目导出为Excel文件
   - 批量导出多个项目
   - 完全按照标准格式导出
   - 每个项目作为独立工作表

4. **增强的用户界面**
   - 表格式数据输入界面
   - 实时数据修改检测
   - 可滚动的大界面设计
   - 直观的结果显示

## 文件结构

```
├── metro_evacuation_calculator_v2.py    # V2.0核心计算模块
├── metro_evacuation_gui_v2.py          # V2.0 GUI界面程序
├── database_manager.py                 # 数据库管理模块
├── excel_exporter.py                   # Excel导出模块
├── start_v2.py                         # V2.0启动脚本
├── test_v2_simple.py                   # V2.0功能测试脚本
├── data.db                             # SQLite数据库文件（自动创建）
├── README_V2.md                        # V2.0使用说明
└── 工作簿1.xlsx                        # Excel导出格式参考
```

## 安装要求

### 必需的Python库
- `tkinter` (GUI界面)
- `sqlite3` (数据库)
- `openpyxl` (Excel处理)
- `datetime` (时间处理)

### 安装命令
```bash
pip install openpyxl
```

注：`tkinter`、`sqlite3`、`datetime` 通常随Python标准库安装。

## 使用方法

### 1. 启动程序

```bash
# 使用启动脚本（推荐）
python start_v2.py

# 或直接启动GUI
python metro_evacuation_gui_v2.py
```

### 2. 界面布局

程序界面分为以下几个区域：

#### 顶部按钮区
- **计算**: 执行所有阶段的计算
- **重置**: 清空所有数据并恢复默认值
- **加载示例**: 加载示例数据进行测试
- **保存数据**: 保存当前项目到数据库
- **读取数据**: 从数据库读取已保存的项目
- **导出Excel**: 导出当前项目为Excel文件
- **批量导出**: 导出所有项目为Excel文件

#### 客流数据区域
表格式输入界面，包含：
- 三个阶段：初期、近期、远期
- 上行数据：上客、下客、断面
- 下行数据：上客、下客、断面

#### 计算参数区域
- **高峰系数**: 每个阶段可设置不同值
- **其他参数**: 所有阶段共用（列车对数、站台参数等）

#### 中间计算值区域
显示每个阶段的中间计算结果：
- 单侧上下客合计（上行/下行）
- 上客流量合计

#### 最终计算结果区域
显示每个阶段的最终结果：
- 站台宽度 b1、b2
- 疏散人数 Q1、Q2
- 疏散时间 T

### 3. 基本操作流程

#### 新建项目
1. 启动程序
2. 输入各阶段的客流数据
3. 调整计算参数（如需要）
4. 点击"计算"按钮
5. 点击"保存数据"，输入站点名称

#### 读取项目
1. 点击"读取数据"按钮
2. 在项目列表中选择目标项目
3. 双击或点击"加载"按钮
4. 数据自动填入界面

#### 修改项目
1. 读取已有项目
2. 修改数据
3. 重新计算
4. 保存时选择"覆盖"或"另存为"

#### 导出数据
1. 确保项目已保存
2. 点击"导出Excel"导出当前项目
3. 或点击"批量导出"导出所有项目

## 计算公式

### 精确Excel公式（三阶段通用）

#### 站台宽度计算
```
b1 = (上行单侧上下客合计 × 高峰系数 ÷ 列车对数 × 站台人流密度 ÷ 站台长度) + 站台边缘距离
b2 = (下行单侧上下客合计 × 高峰系数 ÷ 列车对数 × 站台人流密度 ÷ 站台长度) + 站台边缘距离
```

#### 疏散人数计算
```
Q1 = IF(上行断面 < 下行断面, 下行断面÷列车对数×高峰系数, 上行断面÷列车对数×高峰系数)
Q2 = 上客流量合计 ÷ 列车对数 × 高峰系数
```

#### 疏散时间计算
```
T = (Q1 + Q2) ÷ 0.9 ÷ (自动扶梯通过能力÷60×(自动扶梯台数-1) + 疏散楼梯通过能力÷60×疏散楼梯数×疏散楼梯宽度)
```

## 数据库功能

### 项目管理
- **保存**: 将当前数据保存为新项目或覆盖现有项目
- **读取**: 从数据库加载已保存的项目
- **搜索**: 按站点名称搜索项目
- **删除**: 删除不需要的项目

### 数据保护
- 数据修改检测：修改数据后会提示保存选项
- 覆盖保护：覆盖现有项目时会确认
- 另存为：可将修改后的数据保存为新项目

## Excel导出功能

### 导出格式
完全按照 `工作簿1.xlsx` 的格式导出，包含：
- 客流数据表格
- 计算参数表格
- 中间计算值表格
- 最终计算结果表格

### 导出选项
1. **单项目导出**: 导出当前项目为单个Excel文件
2. **批量导出**: 将所有项目导出到一个Excel文件，每个项目作为独立工作表

## 测试验证

### 运行测试
```bash
# 功能测试
python test_v2_simple.py
```

### 测试内容
- V2计算器功能测试
- 数据库存储和读取测试
- Excel导出功能测试

## 示例数据

程序内置示例数据：

| 阶段 | 上行进客 | 上行出客 | 上行断面 | 下行进客 | 下行出客 | 下行断面 |
|------|----------|----------|----------|----------|----------|----------|
| 初期 | 2767 | 331 | 7785 | 654 | 739 | 6393 |
| 近期 | 3200 | 400 | 8500 | 750 | 850 | 7000 |
| 远期 | 3800 | 500 | 9500 | 900 | 1000 | 8000 |

## 技术特点

1. **模块化设计**: 计算器、数据库、导出功能独立模块
2. **数据完整性**: 完整的数据验证和错误处理
3. **用户友好**: 直观的界面设计和操作流程
4. **扩展性强**: 易于添加新功能和修改

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本（建议3.7+）
   - 安装缺失的依赖库

2. **数据库错误**
   - 检查文件权限
   - 删除损坏的data.db文件重新创建

3. **Excel导出失败**
   - 确保安装了openpyxl库
   - 检查目标文件夹的写入权限

### 获取帮助
如遇到问题，请检查：
1. 错误信息提示
2. 测试脚本运行结果
3. 数据输入格式是否正确

## 版本历史

### V2.0.0 (2025-07-31)
- ✨ 新增三阶段计算支持
- ✨ 新增SQLite数据库存储
- ✨ 新增Excel导出功能
- ✨ 全新的表格式界面设计
- ✨ 项目管理和搜索功能
- ✨ 数据修改保护机制

### V1.0.0 (2024-07-31)
- 🎯 基础计算功能
- 🎯 简单GUI界面
- 🎯 Excel公式精确实现

---

**地铁站台疏散时间计算器 V2.0** - 专业、准确、易用的地铁站台疏散分析工具
