#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
地铁站台疏散时间计算器启动脚本
"""

import sys
import os

def check_dependencies():
    """检查依赖库"""
    required_modules = ['tkinter', 'pandas']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("缺少以下依赖库:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请运行以下命令安装:")
        print("pip install pandas xlrd openpyxl")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("地铁站台疏散时间计算器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    print("正在启动GUI界面...")
    
    try:
        # 导入并启动GUI
        from metro_evacuation_gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"启动失败: {e}")
        print("\n请确保所有文件都在同一目录下:")
        print("  - metro_evacuation_calculator.py")
        print("  - metro_evacuation_gui.py")
        print("  - start.py")
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
