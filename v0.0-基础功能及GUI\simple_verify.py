#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化版功能验证脚本
"""

def test_calculator():
    """测试计算器功能"""
    print("测试计算器模块...")
    try:
        from metro_evacuation_calculator import MetroEvacuationCalculator
        
        calc = MetroEvacuationCalculator()
        calc.set_passenger_flow(2767, 331, 7785, 654, 739, 6393)
        calc.set_parameters(k=1.13, n_train=20, p=0.5, l=135.6, m=0.3,
                           a_1=7300, a_2=3700, n_auto=2, b=3.3, n_stair=1)
        calc.calculate_all()
        
        results = calc.get_results()
        
        # 验证关键结果
        expected = {
            'b_1': 0.945417,
            'b_2': 0.590208,
            'q_1': 439.8525,
            'q_2': 193.2865,
            't': 2.163468
        }
        
        print("验证计算结果:")
        all_correct = True
        for key, exp_val in expected.items():
            actual = results[key]
            error = abs(actual - exp_val) / exp_val * 100
            if error > 0.001:
                all_correct = False
                print(f"  {key}: 失败 - 期望{exp_val}, 实际{actual:.6f}")
            else:
                print(f"  {key}: 通过 - {actual:.6f}")
        
        if all_correct:
            print("计算器模块测试: 通过")
            return True
        else:
            print("计算器模块测试: 失败")
            return False
            
    except Exception as e:
        print(f"计算器模块测试: 错误 - {e}")
        return False

def test_gui_import():
    """测试GUI模块导入"""
    print("\n测试GUI模块导入...")
    try:
        import tkinter as tk
        from metro_evacuation_gui import MetroEvacuationGUI
        print("GUI模块导入: 通过")
        return True
    except Exception as e:
        print(f"GUI模块导入: 失败 - {e}")
        return False

def test_formulas():
    """测试公式逻辑"""
    print("\n测试公式逻辑...")
    try:
        from metro_evacuation_calculator import MetroEvacuationCalculator
        
        calc = MetroEvacuationCalculator()
        calc.set_passenger_flow(2767, 331, 7785, 654, 739, 6393)
        calc.set_parameters(k=1.13, n_train=20, p=0.5, l=135.6, m=0.3,
                           a_1=7300, a_2=3700, n_auto=2, b=3.3, n_stair=1)
        calc.calculate_all()
        
        # 验证中间计算值
        if calc.up_add == calc.up_in + calc.up_out:
            print("  up_add公式: 通过")
        else:
            print("  up_add公式: 失败")
            return False
            
        if calc.down_add == calc.down_in + calc.down_out:
            print("  down_add公式: 通过")
        else:
            print("  down_add公式: 失败")
            return False
            
        if calc.in_add == calc.up_in + calc.down_in:
            print("  in_add公式: 通过")
        else:
            print("  in_add公式: 失败")
            return False
        
        # 验证站台宽度公式
        expected_b1 = (calc.up_add * calc.k / calc.n_train * calc.p / calc.l) + calc.m
        if abs(calc.b_1 - expected_b1) < 1e-6:
            print("  b1公式: 通过")
        else:
            print("  b1公式: 失败")
            return False
            
        expected_b2 = (calc.down_add * calc.k / calc.n_train * calc.p / calc.l) + calc.m
        if abs(calc.b_2 - expected_b2) < 1e-6:
            print("  b2公式: 通过")
        else:
            print("  b2公式: 失败")
            return False
        
        print("公式逻辑测试: 通过")
        return True
        
    except Exception as e:
        print(f"公式逻辑测试: 错误 - {e}")
        return False

def main():
    """主函数"""
    print("地铁站台疏散时间计算器 - 功能验证")
    print("=" * 40)
    
    tests = [
        ("计算器功能", test_calculator),
        ("GUI模块导入", test_gui_import),
        ("公式逻辑", test_formulas)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
    
    print("\n" + "=" * 40)
    print("测试结果汇总:")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("所有测试通过！系统运行正常！")
        return True
    else:
        print(f"{total-passed} 个测试失败。")
        return False

if __name__ == "__main__":
    main()
