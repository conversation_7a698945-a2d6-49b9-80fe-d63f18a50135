#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
精确公式测试脚本
验证从Excel表格中提取的公式是否完全准确
"""

from metro_evacuation_calculator import MetroEvacuationCalculator

def test_exact_excel_formulas():
    """测试精确的Excel公式"""
    print("=== 精确Excel公式测试 ===")
    
    # 创建计算器实例
    calc = MetroEvacuationCalculator()
    
    # 设置Excel表格中的原始数据
    calc.set_passenger_flow(
        up_in=2767,      # C2: 上行进客
        up_out=331,      # C3: 上行出客  
        up_area=7785,    # C4: 上行断面
        down_in=654,     # C5: 下行进客
        down_out=739,    # C6: 下行出客
        down_area=6393   # C7: 下行断面
    )
    
    # 设置Excel表格中的计算参数
    calc.set_parameters(
        k=1.13,          # C13: 高峰系数
        n_train=20,      # C14: 列车对数
        p=0.5,           # C15: 站台人流密度
        l=135.6,         # C16: 站台长度
        m=0.3,           # C17: 站台边缘距离
        a_1=7300,        # C18: 自动扶梯通过能力
        a_2=3700,        # C19: 疏散楼梯通过能力
        n_auto=2,        # C20: 自动扶梯台数
        b=3.3,           # C21: 疏散楼梯宽度
        n_stair=1        # C22: 疏散楼梯数
    )
    
    # 执行计算
    calc.calculate_all()
    
    # Excel中的期望结果
    expected_results = {
        'up_add': 3098,        # C8 = C2+C3
        'down_add': 1393,      # C9 = C5+C6  
        'in_add': 3421,        # C10 = C2+C5
        'b_1': 0.945417,       # C25 = C8*C13/C14*C15/C16+C17
        'b_2': 0.590208,       # C26 = C9*C13/C14*C15/C16+C17
        'q_1': 439.8525,       # C27 = IF(C4<C7,C7/C14*C13,C4/C14*C13)
        'q_2': 193.2865,       # C28 = C10/C14*C13
        't': 2.163468          # C29 = (C27+C28)/0.9/(C18/60*(C20-1)+C19/60*C22*C21)
    }
    
    # 获取计算结果
    results = calc.get_results()
    
    print("\n=== 详细对比结果 ===")
    print(f"{'参数':<15} {'计算值':<15} {'Excel值':<15} {'误差(%)':<10} {'状态'}")
    print("-" * 70)
    
    all_passed = True
    total_error = 0
    
    for key, expected in expected_results.items():
        actual = results[key]
        error_pct = abs(actual - expected) / expected * 100 if expected != 0 else 0
        total_error += error_pct
        
        status = "通过" if error_pct < 0.001 else "失败"
        if error_pct >= 0.001:
            all_passed = False
            
        print(f"{key:<15} {actual:<15.6f} {expected:<15.6f} {error_pct:<10.4f} {status}")
    
    avg_error = total_error / len(expected_results)
    
    print("-" * 70)
    print(f"平均误差: {avg_error:.6f}%")
    print(f"总体状态: {'所有测试通过' if all_passed else '部分测试失败'}")
    
    # 验证公式逻辑
    print("\n=== 公式验证 ===")
    
    # 验证中间计算值
    print(f"C8 (up_add) = C2 + C3 = {calc.up_in} + {calc.up_out} = {calc.up_add}")
    print(f"C9 (down_add) = C5 + C6 = {calc.down_in} + {calc.down_out} = {calc.down_add}")
    print(f"C10 (in_add) = C2 + C5 = {calc.up_in} + {calc.down_in} = {calc.in_add}")
    
    # 验证站台宽度公式
    print(f"\nC25 (b1) = C8*C13/C14*C15/C16+C17")
    print(f"         = {calc.up_add}*{calc.k}/{calc.n_train}*{calc.p}/{calc.l}+{calc.m}")
    print(f"         = {calc.b_1:.6f}")
    
    print(f"\nC26 (b2) = C9*C13/C14*C15/C16+C17")
    print(f"         = {calc.down_add}*{calc.k}/{calc.n_train}*{calc.p}/{calc.l}+{calc.m}")
    print(f"         = {calc.b_2:.6f}")
    
    # 验证Q1公式
    print(f"\nC27 (Q1) = IF(C4<C7, C7/C14*C13, C4/C14*C13)")
    print(f"         = IF({calc.up_area}<{calc.down_area}, {calc.down_area}/{calc.n_train}*{calc.k}, {calc.up_area}/{calc.n_train}*{calc.k})")
    if calc.up_area < calc.down_area:
        print(f"         = {calc.down_area}/{calc.n_train}*{calc.k} = {calc.q_1:.4f}")
    else:
        print(f"         = {calc.up_area}/{calc.n_train}*{calc.k} = {calc.q_1:.4f}")
    
    # 验证Q2公式
    print(f"\nC28 (Q2) = C10/C14*C13")
    print(f"         = {calc.in_add}/{calc.n_train}*{calc.k}")
    print(f"         = {calc.q_2:.4f}")
    
    # 验证疏散时间公式
    escalator_cap = calc.a_1 / 60 * (calc.n_auto - 1)
    stair_cap = calc.a_2 / 60 * calc.n_stair * calc.b
    print(f"\nC29 (T) = (C27+C28)/0.9/(C18/60*(C20-1)+C19/60*C22*C21)")
    print(f"        = ({calc.q_1}+{calc.q_2})/0.9/({calc.a_1}/60*({calc.n_auto}-1)+{calc.a_2}/60*{calc.n_stair}*{calc.b})")
    print(f"        = {calc.q_1 + calc.q_2}/0.9/({escalator_cap:.4f}+{stair_cap:.4f})")
    print(f"        = {calc.t:.6f}")
    
    return all_passed

def test_parameter_mapping():
    """测试参数映射关系"""
    print("\n=== 参数映射关系验证 ===")
    
    calc = MetroEvacuationCalculator()
    
    # 验证参数映射
    mapping = {
        'C2': ('up_in', '上行进客'),
        'C3': ('up_out', '上行出客'),
        'C4': ('up_area', '上行断面'),
        'C5': ('down_in', '下行进客'),
        'C6': ('down_out', '下行出客'),
        'C7': ('down_area', '下行断面'),
        'C13': ('k', '高峰系数'),
        'C14': ('n_train', '列车对数'),
        'C15': ('p', '站台人流密度'),
        'C16': ('l', '站台长度'),
        'C17': ('m', '站台边缘距离'),
        'C18': ('a_1', '自动扶梯通过能力'),
        'C19': ('a_2', '疏散楼梯通过能力'),
        'C20': ('n_auto', '自动扶梯台数'),
        'C21': ('b', '疏散楼梯宽度'),
        'C22': ('n_stair', '疏散楼梯数')
    }
    
    print(f"{'Excel位置':<10} {'变量名':<15} {'参数名称':<20}")
    print("-" * 50)
    for excel_pos, (var_name, param_name) in mapping.items():
        print(f"{excel_pos:<10} {var_name:<15} {param_name:<20}")
    
    print("\n参数映射关系验证完成！")

if __name__ == "__main__":
    # 运行测试
    success = test_exact_excel_formulas()
    test_parameter_mapping()
    
    print("\n" + "="*70)
    if success:
        print("所有测试通过！公式提取和实现完全正确！")
    else:
        print("部分测试失败，需要检查公式实现。")
    print("="*70)
