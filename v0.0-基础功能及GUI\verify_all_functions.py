#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整功能验证脚本
验证所有模块是否正常工作
"""

import sys
import traceback

def test_calculator_module():
    """测试计算器模块"""
    print("=== 测试计算器模块 ===")
    try:
        from metro_evacuation_calculator import MetroEvacuationCalculator
        
        calc = MetroEvacuationCalculator()
        calc.set_passenger_flow(2767, 331, 7785, 654, 739, 6393)
        calc.set_parameters(k=1.13, n_train=20, p=0.5, l=135.6, m=0.3,
                           a_1=7300, a_2=3700, n_auto=2, b=3.3, n_stair=1)
        calc.calculate_all()
        
        results = calc.get_results()
        print(f"计算器模块正常，b1={results['b_1']:.6f}")
        return True
    except Exception as e:
        print(f"计算器模块错误: {e}")
        traceback.print_exc()
        return False

def test_gui_module():
    """测试GUI模块导入"""
    print("\n=== 测试GUI模块 ===")
    try:
        # 只测试导入，不启动GUI
        import tkinter as tk
        from metro_evacuation_gui import MetroEvacuationGUI
        print("GUI模块导入正常")
        return True
    except Exception as e:
        print(f"GUI模块错误: {e}")
        traceback.print_exc()
        return False

def test_exact_formulas():
    """测试精确公式"""
    print("\n=== 测试精确公式 ===")
    try:
        from metro_evacuation_calculator import MetroEvacuationCalculator
        
        calc = MetroEvacuationCalculator()
        calc.set_passenger_flow(2767, 331, 7785, 654, 739, 6393)
        calc.set_parameters(k=1.13, n_train=20, p=0.5, l=135.6, m=0.3,
                           a_1=7300, a_2=3700, n_auto=2, b=3.3, n_stair=1)
        calc.calculate_all()
        
        # 验证关键结果
        expected = {
            'b_1': 0.945417,
            'b_2': 0.590208,
            'q_1': 439.8525,
            'q_2': 193.2865,
            't': 2.163468
        }
        
        results = calc.get_results()
        all_correct = True
        
        for key, exp_val in expected.items():
            actual = results[key]
            error = abs(actual - exp_val) / exp_val * 100
            if error > 0.001:  # 0.001% 误差阈值
                all_correct = False
                print(f"✗ {key}: 期望{exp_val}, 实际{actual}, 误差{error:.4f}%")
            else:
                print(f"✓ {key}: {actual:.6f} (误差{error:.6f}%)")
        
        if all_correct:
            print("✓ 所有公式验证通过")
        else:
            print("✗ 部分公式验证失败")
            
        return all_correct
    except Exception as e:
        print(f"✗ 公式测试错误: {e}")
        traceback.print_exc()
        return False

def test_parameter_mapping():
    """测试参数映射"""
    print("\n=== 测试参数映射 ===")
    try:
        from metro_evacuation_calculator import MetroEvacuationCalculator
        
        calc = MetroEvacuationCalculator()
        
        # 测试参数设置
        calc.set_passenger_flow(100, 200, 300, 400, 500, 600)
        calc.set_parameters(k=1.5, n_train=25, p=0.6)
        
        # 验证参数是否正确设置
        params = calc.get_input_parameters()
        
        expected_params = {
            'up_in': 100, 'up_out': 200, 'up_area': 300,
            'down_in': 400, 'down_out': 500, 'down_area': 600,
            'k': 1.5, 'n_train': 25, 'p': 0.6
        }
        
        all_correct = True
        for key, expected in expected_params.items():
            if params[key] != expected:
                print(f"✗ {key}: 期望{expected}, 实际{params[key]}")
                all_correct = False
        
        if all_correct:
            print("✓ 参数映射正确")
        else:
            print("✗ 参数映射错误")
            
        return all_correct
    except Exception as e:
        print(f"✗ 参数映射测试错误: {e}")
        traceback.print_exc()
        return False

def test_formula_logic():
    """测试公式逻辑"""
    print("\n=== 测试公式逻辑 ===")
    try:
        from metro_evacuation_calculator import MetroEvacuationCalculator
        
        calc = MetroEvacuationCalculator()
        calc.set_passenger_flow(2767, 331, 7785, 654, 739, 6393)
        calc.set_parameters(k=1.13, n_train=20, p=0.5, l=135.6, m=0.3,
                           a_1=7300, a_2=3700, n_auto=2, b=3.3, n_stair=1)
        calc.calculate_all()
        
        # 验证中间计算值
        expected_up_add = calc.up_in + calc.up_out  # 2767 + 331 = 3098
        expected_down_add = calc.down_in + calc.down_out  # 654 + 739 = 1393
        expected_in_add = calc.up_in + calc.down_in  # 2767 + 654 = 3421
        
        if (calc.up_add == expected_up_add and 
            calc.down_add == expected_down_add and 
            calc.in_add == expected_in_add):
            print("✓ 中间计算值正确")
        else:
            print("✗ 中间计算值错误")
            return False
        
        # 验证站台宽度公式
        expected_b1 = (calc.up_add * calc.k / calc.n_train * calc.p / calc.l) + calc.m
        expected_b2 = (calc.down_add * calc.k / calc.n_train * calc.p / calc.l) + calc.m
        
        if (abs(calc.b_1 - expected_b1) < 1e-6 and 
            abs(calc.b_2 - expected_b2) < 1e-6):
            print("✓ 站台宽度公式正确")
        else:
            print("✗ 站台宽度公式错误")
            return False
        
        # 验证Q1公式逻辑
        if calc.up_area < calc.down_area:
            expected_q1 = (calc.down_area / calc.n_train) * calc.k
        else:
            expected_q1 = (calc.up_area / calc.n_train) * calc.k
            
        if abs(calc.q_1 - expected_q1) < 1e-6:
            print("✓ Q1公式逻辑正确")
        else:
            print("✗ Q1公式逻辑错误")
            return False
        
        # 验证Q2公式
        expected_q2 = (calc.in_add / calc.n_train) * calc.k
        if abs(calc.q_2 - expected_q2) < 1e-6:
            print("✓ Q2公式正确")
        else:
            print("✗ Q2公式错误")
            return False
        
        print("✓ 所有公式逻辑验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 公式逻辑测试错误: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("地铁站台疏散时间计算器 - 完整功能验证")
    print("=" * 50)
    
    tests = [
        ("计算器模块", test_calculator_module),
        ("GUI模块", test_gui_module),
        ("精确公式", test_exact_formulas),
        ("参数映射", test_parameter_mapping),
        ("公式逻辑", test_formula_logic)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 30)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统运行正常！")
        return True
    else:
        print(f"\n❌ {total-passed} 个测试失败，需要检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
