#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
地铁站台疏散时间计算器 V2.0 启动脚本
"""

import sys
import os

def check_dependencies():
    """检查依赖库"""
    required_modules = [
        'tkinter',
        'sqlite3', 
        'openpyxl',
        'datetime'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"✗ {module} - 缺失")
    
    if missing_modules:
        print(f"\n缺少以下依赖库: {', '.join(missing_modules)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

def main():
    """主函数"""
    print("地铁站台疏散时间计算器 V2.0")
    print("=" * 40)
    
    print("检查依赖库...")
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    print("\n所有依赖库检查通过！")
    print("启动GUI界面...")
    
    try:
        from metro_evacuation_gui_v2 import main as gui_main
        gui_main()
    except Exception as e:
        print(f"启动失败: {e}")
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
