#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库管理模块
用于管理地铁站台疏散计算数据的SQLite数据库
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data.db"):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 删除旧表（如果存在）
            cursor.execute('DROP TABLE IF EXISTS projects')

            # 创建新的项目表
            cursor.execute('''
                CREATE TABLE projects (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    station_name TEXT UNIQUE NOT NULL,
                    created_time TEXT NOT NULL,
                    updated_time TEXT NOT NULL,
                    
                    -- 客流数据 (初期)
                    initial_up_in REAL DEFAULT 0,
                    initial_up_out REAL DEFAULT 0,
                    initial_up_area REAL DEFAULT 0,
                    initial_down_in REAL DEFAULT 0,
                    initial_down_out REAL DEFAULT 0,
                    initial_down_area REAL DEFAULT 0,
                    
                    -- 客流数据 (近期)
                    near_up_in REAL DEFAULT 0,
                    near_up_out REAL DEFAULT 0,
                    near_up_area REAL DEFAULT 0,
                    near_down_in REAL DEFAULT 0,
                    near_down_out REAL DEFAULT 0,
                    near_down_area REAL DEFAULT 0,
                    
                    -- 客流数据 (远期)
                    far_up_in REAL DEFAULT 0,
                    far_up_out REAL DEFAULT 0,
                    far_up_area REAL DEFAULT 0,
                    far_down_in REAL DEFAULT 0,
                    far_down_out REAL DEFAULT 0,
                    far_down_area REAL DEFAULT 0,
                    
                    -- 计算参数 (初期)
                    k_initial REAL DEFAULT 1.13,
                    n_train_initial REAL DEFAULT 20,
                    p_initial REAL DEFAULT 0.5,
                    l_initial REAL DEFAULT 135.6,
                    m_initial REAL DEFAULT 0.3,
                    a_1_initial REAL DEFAULT 7300,
                    a_2_initial REAL DEFAULT 3700,
                    n_auto_initial REAL DEFAULT 2,
                    b_initial REAL DEFAULT 3.3,
                    n_stair_initial REAL DEFAULT 1,

                    -- 计算参数 (近期)
                    k_near REAL DEFAULT 1.13,
                    n_train_near REAL DEFAULT 20,
                    p_near REAL DEFAULT 0.5,
                    l_near REAL DEFAULT 135.6,
                    m_near REAL DEFAULT 0.3,
                    a_1_near REAL DEFAULT 7300,
                    a_2_near REAL DEFAULT 3700,
                    n_auto_near REAL DEFAULT 2,
                    b_near REAL DEFAULT 3.3,
                    n_stair_near REAL DEFAULT 1,

                    -- 计算参数 (远期)
                    k_far REAL DEFAULT 1.13,
                    n_train_far REAL DEFAULT 20,
                    p_far REAL DEFAULT 0.5,
                    l_far REAL DEFAULT 135.6,
                    m_far REAL DEFAULT 0.3,
                    a_1_far REAL DEFAULT 7300,
                    a_2_far REAL DEFAULT 3700,
                    n_auto_far REAL DEFAULT 2,
                    b_far REAL DEFAULT 3.3,
                    n_stair_far REAL DEFAULT 1,
                    
                    -- 中间计算值 (初期)
                    initial_up_add REAL DEFAULT 0,
                    initial_down_add REAL DEFAULT 0,
                    initial_in_add REAL DEFAULT 0,
                    
                    -- 中间计算值 (近期)
                    near_up_add REAL DEFAULT 0,
                    near_down_add REAL DEFAULT 0,
                    near_in_add REAL DEFAULT 0,
                    
                    -- 中间计算值 (远期)
                    far_up_add REAL DEFAULT 0,
                    far_down_add REAL DEFAULT 0,
                    far_in_add REAL DEFAULT 0,
                    
                    -- 最终计算结果 (初期)
                    initial_b_1 REAL DEFAULT 0,
                    initial_b_2 REAL DEFAULT 0,
                    initial_q_1 REAL DEFAULT 0,
                    initial_q_2 REAL DEFAULT 0,
                    initial_t REAL DEFAULT 0,
                    
                    -- 最终计算结果 (近期)
                    near_b_1 REAL DEFAULT 0,
                    near_b_2 REAL DEFAULT 0,
                    near_q_1 REAL DEFAULT 0,
                    near_q_2 REAL DEFAULT 0,
                    near_t REAL DEFAULT 0,
                    
                    -- 最终计算结果 (远期)
                    far_b_1 REAL DEFAULT 0,
                    far_b_2 REAL DEFAULT 0,
                    far_q_1 REAL DEFAULT 0,
                    far_q_2 REAL DEFAULT 0,
                    far_t REAL DEFAULT 0
                )
            ''')
            
            conn.commit()
    
    def save_project(self, station_name: str, data: Dict) -> bool:
        """保存项目数据"""
        try:
            current_time = datetime.now().isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在
                cursor.execute('SELECT id FROM projects WHERE station_name = ?', (station_name,))
                exists = cursor.fetchone()
                
                if exists:
                    # 更新现有记录
                    update_sql = '''
                        UPDATE projects SET
                        updated_time = ?,
                        initial_up_in = ?, initial_up_out = ?, initial_up_area = ?,
                        initial_down_in = ?, initial_down_out = ?, initial_down_area = ?,
                        near_up_in = ?, near_up_out = ?, near_up_area = ?,
                        near_down_in = ?, near_down_out = ?, near_down_area = ?,
                        far_up_in = ?, far_up_out = ?, far_up_area = ?,
                        far_down_in = ?, far_down_out = ?, far_down_area = ?,
                        k_initial = ?, n_train_initial = ?, p_initial = ?, l_initial = ?, m_initial = ?,
                        a_1_initial = ?, a_2_initial = ?, n_auto_initial = ?, b_initial = ?, n_stair_initial = ?,
                        k_near = ?, n_train_near = ?, p_near = ?, l_near = ?, m_near = ?,
                        a_1_near = ?, a_2_near = ?, n_auto_near = ?, b_near = ?, n_stair_near = ?,
                        k_far = ?, n_train_far = ?, p_far = ?, l_far = ?, m_far = ?,
                        a_1_far = ?, a_2_far = ?, n_auto_far = ?, b_far = ?, n_stair_far = ?,
                        initial_up_add = ?, initial_down_add = ?, initial_in_add = ?,
                        near_up_add = ?, near_down_add = ?, near_in_add = ?,
                        far_up_add = ?, far_down_add = ?, far_in_add = ?,
                        initial_b_1 = ?, initial_b_2 = ?, initial_q_1 = ?, initial_q_2 = ?, initial_t = ?,
                        near_b_1 = ?, near_b_2 = ?, near_q_1 = ?, near_q_2 = ?, near_t = ?,
                        far_b_1 = ?, far_b_2 = ?, far_q_1 = ?, far_q_2 = ?, far_t = ?
                        WHERE station_name = ?
                    '''
                    
                    values = [current_time] + self._extract_values_from_data(data) + [station_name]
                    cursor.execute(update_sql, values)
                else:
                    # 插入新记录
                    insert_sql = '''
                        INSERT INTO projects (
                            station_name, created_time, updated_time,
                            initial_up_in, initial_up_out, initial_up_area,
                            initial_down_in, initial_down_out, initial_down_area,
                            near_up_in, near_up_out, near_up_area,
                            near_down_in, near_down_out, near_down_area,
                            far_up_in, far_up_out, far_up_area,
                            far_down_in, far_down_out, far_down_area,
                            k_initial, n_train_initial, p_initial, l_initial, m_initial,
                            a_1_initial, a_2_initial, n_auto_initial, b_initial, n_stair_initial,
                            k_near, n_train_near, p_near, l_near, m_near,
                            a_1_near, a_2_near, n_auto_near, b_near, n_stair_near,
                            k_far, n_train_far, p_far, l_far, m_far,
                            a_1_far, a_2_far, n_auto_far, b_far, n_stair_far,
                            initial_up_add, initial_down_add, initial_in_add,
                            near_up_add, near_down_add, near_in_add,
                            far_up_add, far_down_add, far_in_add,
                            initial_b_1, initial_b_2, initial_q_1, initial_q_2, initial_t,
                            near_b_1, near_b_2, near_q_1, near_q_2, near_t,
                            far_b_1, far_b_2, far_q_1, far_q_2, far_t
                        ) VALUES (''' + ', '.join(['?'] * 75) + ''')
                    '''
                    
                    values = [station_name, current_time, current_time] + self._extract_values_from_data(data)
                    cursor.execute(insert_sql, values)
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"保存数据时出错: {e}")
            return False
    
    def _extract_values_from_data(self, data: Dict) -> List:
        """从数据字典中提取值列表"""
        # 确保所有字段都有默认值
        values = []

        # 客流数据 - 18个字段
        for period in ['initial', 'near', 'far']:
            for flow_type in ['up_in', 'up_out', 'up_area', 'down_in', 'down_out', 'down_area']:
                values.append(data.get(f'{period}_{flow_type}', 0))

        # 计算参数 - 33个字段 (每个阶段11个参数)
        # 初期参数
        values.extend([
            data.get('k_initial', 1.13), data.get('n_train_initial', 20), data.get('p_initial', 0.5),
            data.get('l_initial', 135.6), data.get('m_initial', 0.3), data.get('a_1_initial', 7300),
            data.get('a_2_initial', 3700), data.get('n_auto_initial', 2), data.get('b_initial', 3.3),
            data.get('n_stair_initial', 1)
        ])
        # 近期参数
        values.extend([
            data.get('k_near', 1.13), data.get('n_train_near', 20), data.get('p_near', 0.5),
            data.get('l_near', 135.6), data.get('m_near', 0.3), data.get('a_1_near', 7300),
            data.get('a_2_near', 3700), data.get('n_auto_near', 2), data.get('b_near', 3.3),
            data.get('n_stair_near', 1)
        ])
        # 远期参数
        values.extend([
            data.get('k_far', 1.13), data.get('n_train_far', 20), data.get('p_far', 0.5),
            data.get('l_far', 135.6), data.get('m_far', 0.3), data.get('a_1_far', 7300),
            data.get('a_2_far', 3700), data.get('n_auto_far', 2), data.get('b_far', 3.3),
            data.get('n_stair_far', 1)
        ])

        # 中间计算值 - 9个字段
        for period in ['initial', 'near', 'far']:
            for calc_type in ['up_add', 'down_add', 'in_add']:
                values.append(data.get(f'{period}_{calc_type}', 0))

        # 最终结果 - 15个字段
        for period in ['initial', 'near', 'far']:
            for result_type in ['b_1', 'b_2', 'q_1', 'q_2', 't']:
                values.append(data.get(f'{period}_{result_type}', 0))

        return values
    
    def load_project(self, station_name: str) -> Optional[Dict]:
        """加载项目数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM projects WHERE station_name = ?', (station_name,))
                row = cursor.fetchone()
                
                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                return None
                
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return None
    
    def get_all_projects(self) -> List[Tuple[str, str]]:
        """获取所有项目列表 (站名, 更新时间)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT station_name, updated_time FROM projects ORDER BY updated_time DESC')
                return cursor.fetchall()
                
        except Exception as e:
            print(f"获取项目列表时出错: {e}")
            return []
    
    def delete_project(self, station_name: str) -> bool:
        """删除项目"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM projects WHERE station_name = ?', (station_name,))
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            print(f"删除项目时出错: {e}")
            return False
    
    def search_projects(self, keyword: str) -> List[Tuple[str, str]]:
        """搜索项目"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT station_name, updated_time FROM projects WHERE station_name LIKE ? ORDER BY updated_time DESC',
                    (f'%{keyword}%',)
                )
                return cursor.fetchall()
                
        except Exception as e:
            print(f"搜索项目时出错: {e}")
            return []
